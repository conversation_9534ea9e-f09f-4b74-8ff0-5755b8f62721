#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MIPと多スタート焼きなまし法の比較実行スクリプト
"""

import os
import glob
import time
import pandas as pd
import matplotlib.pyplot as plt
import japanize_matplotlib
from process import (
    save_results_to_csv, calculate_summary_statistics
)
# グローバル変数をインポート
import process

def load_results_from_csv(result_folder='result'):
    """resultフォルダからMIPとSAの結果を読み込む関数"""
    mip_results = []
    sa_results = []

    # MIP結果ファイルを読み込み
    mip_files = glob.glob(os.path.join(result_folder, 'MIP_results_*.csv'))
    for mip_file in mip_files:
        try:
            df = pd.read_csv(mip_file)
            if not df.empty:
                result = df.iloc[0].to_dict()
                mip_results.append(result)
                print(f"MIP結果を読み込み: {os.path.basename(mip_file)}")
        except Exception as e:
            print(f"MIPファイル読み込みエラー {mip_file}: {e}")

    # SA結果ファイルを読み込み
    sa_files = glob.glob(os.path.join(result_folder, 'SA_results_*.csv'))
    for sa_file in sa_files:
        try:
            df = pd.read_csv(sa_file)
            if not df.empty:
                result = df.iloc[0].to_dict()
                sa_results.append(result)
                print(f"SA結果を読み込み: {os.path.basename(sa_file)}")
        except Exception as e:
            print(f"SAファイル読み込みエラー {sa_file}: {e}")

    return mip_results, sa_results

def compare_optimization_results(mip_results=None, sa_results=None, title="最適化手法比較"):
    """MIPと焼きなまし法の結果を比較する関数"""
    if not mip_results and not sa_results:
        print("比較するデータがありません。")
        return None, None

    # データの準備
    comparison_data = []

    if mip_results:
        for result in mip_results:
            comparison_data.append({
                'データファイル': result['データファイル'],
                '手法': 'MIP',
                '総コスト': result.get('総コスト', 0),
                '計算時間': result.get('計算時間', 0)
            })

    if sa_results:
        for result in sa_results:
            comparison_data.append({
                'データファイル': result['データファイル'],
                '手法': 'SA',
                '総コスト': result.get('総コスト', 0),
                '計算時間': result.get('計算時間', 0)
            })

    if not comparison_data:
        print("比較データが空です。")
        return None, None

    df = pd.DataFrame(comparison_data)

    # プロット作成
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    # 総コストの比較
    if 'MIP' in df['手法'].values and 'SA' in df['手法'].values:
        mip_data = df[df['手法'] == 'MIP']
        sa_data = df[df['手法'] == 'SA']

        x = range(len(mip_data))
        width = 0.35

        ax1.bar([i - width/2 for i in x], mip_data['総コスト'], width, label='MIP', color='lightblue')
        ax1.bar([i + width/2 for i in x], sa_data['総コスト'], width, label='SA', color='lightcoral')
        ax1.set_title('総コスト比較')
        ax1.set_xlabel('データファイル')
        ax1.set_ylabel('総コスト')
        ax1.set_xticks(x)
        ax1.set_xticklabels(mip_data['データファイル'], rotation=45)
        ax1.legend()

        # 計算時間の比較
        ax2.bar([i - width/2 for i in x], mip_data['計算時間'], width, label='MIP', color='lightblue')
        ax2.bar([i + width/2 for i in x], sa_data['計算時間'], width, label='SA', color='lightcoral')
        ax2.set_title('計算時間比較')
        ax2.set_xlabel('データファイル')
        ax2.set_ylabel('計算時間 (秒)')
        ax2.set_xticks(x)
        ax2.set_xticklabels(mip_data['データファイル'], rotation=45)
        ax2.legend()

    plt.tight_layout()

    # プロットを保存
    os.makedirs('result', exist_ok=True)
    plot_filename = f'result/{title}_comparison_plot.png'
    plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
    print(f"比較プロットを保存: {plot_filename}")

    plt.show()

    # 比較結果をCSVに保存
    comparison_csv = save_results_to_csv(comparison_data, f'{title}_comparison.csv')

    return plot_filename, comparison_csv



def analyze_comparison_results(mip_results, sa_results):
    """MIPとSAの結果を詳細分析する関数"""
    if not mip_results or not sa_results:
        return None
    
    analysis = {}
    
    # データファイル別の比較
    file_comparison = []
    
    for mip_result in mip_results:
        file_name = mip_result['データファイル']
        sa_result = next((r for r in sa_results if r['データファイル'] == file_name), None)
        
        if sa_result:
            cost_diff = sa_result['総コスト'] - mip_result['総コスト']
            time_diff = sa_result['計算時間'] - mip_result['計算時間']
            cost_ratio = sa_result['総コスト'] / mip_result['総コスト'] if mip_result['総コスト'] > 0 else 1
            
            file_comparison.append({
                'データファイル': file_name,
                'MIP総コスト': mip_result['総コスト'],
                'SA総コスト': sa_result['総コスト'],
                'コスト差': cost_diff,
                'コスト比': cost_ratio,
                'MIP計算時間': mip_result['計算時間'],
                'SA計算時間': sa_result['計算時間'],
                '時間差': time_diff,
                'MIPが優秀': cost_diff > 0
            })
    
    if file_comparison:
        comparison_df = pd.DataFrame(file_comparison)
        
        # 統計情報
        analysis['ファイル数'] = len(file_comparison)
        analysis['MIP勝利数'] = sum(comparison_df['MIPが優秀'])
        analysis['SA勝利数'] = len(file_comparison) - analysis['MIP勝利数']
        analysis['平均コスト差'] = comparison_df['コスト差'].mean()
        analysis['平均コスト比'] = comparison_df['コスト比'].mean()
        analysis['平均時間差'] = comparison_df['時間差'].mean()
        
        # 比較結果を保存
        save_results_to_csv(file_comparison, 'Detailed_comparison.csv')
        
        print(f"\n=== 詳細比較分析 ===")
        print(f"比較ファイル数: {analysis['ファイル数']}")
        print(f"MIP勝利: {analysis['MIP勝利数']}ファイル")
        print(f"SA勝利: {analysis['SA勝利数']}ファイル")
        print(f"平均コスト差 (SA - MIP): {analysis['平均コスト差']:.2f}")
        print(f"平均コスト比 (SA / MIP): {analysis['平均コスト比']:.3f}")
        print(f"平均時間差 (SA - MIP): {analysis['平均時間差']:.2f}秒")
    
    return analysis

def main():
    """メイン実行関数"""
    print("=" * 60)
    print("MIP vs 多スタート焼きなまし法 比較実行")
    print("=" * 60)
    
    # データフォルダの確認
    data_folder = 'data'
    if not os.path.exists(data_folder):
        print(f"データフォルダ '{data_folder}' が見つかりません。")
        return
    
    # パラメータ設定
    mip_time_limit = 500
    sa_params = {
        'num_starts': 5,
        'max_iterations': 1000,
        'initial_temp': 1000,
        'cooling_rate': 0.95
    }
    
    print(f"MIP時間制限: {mip_time_limit}秒")
    print(f"SAパラメータ: {sa_params}")
    
    # 比較実行
    result = run_comparison_batch(data_folder, mip_time_limit, sa_params)
    
    if result:
        print(f"\n=== 最終結果サマリー ===")
        print(f"総処理時間: {result['total_time']:.2f}秒")
        print(f"全結果CSV: {result['all_csv']}")
        print(f"比較CSV: {result['comparison_csv']}")
        print(f"統計CSV: {result['stats_csv']}")
        print(f"比較プロット: {result['plot_filename']}")
        
        if result['analysis']:
            analysis = result['analysis']
            print(f"\nMIP勝利率: {analysis['MIP勝利数'] / analysis['ファイル数'] * 100:.1f}%")
            print(f"SA勝利率: {analysis['SA勝利数'] / analysis['ファイル数'] * 100:.1f}%")
    else:
        print("比較処理に失敗しました。")

if __name__ == "__main__":
    main()
