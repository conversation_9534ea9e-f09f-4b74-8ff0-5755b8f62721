{"cells": [{"cell_type": "code", "execution_count": 1, "id": "f57a500f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["見つかったCSVファイル: ['D28.csv', 'D42.csv', 'D40.csv']\n", "\n", "=== Processing data/D28.csv ===\n", "\n", "=== 初期在庫水準の調整アルゴリズム開始 ===\n", "--- 調整イテレーション 1 ---\n", "  今回の調整量: [298.0, 163.0, 366.0, 307.20000000000005, 94.0, 527.0, 12.799999999999997, 670.0, 84.0, 16.799999999999997, 100.80000000000001, 222.0, 281.6]\n", "  更新後の初期在庫量: [1078.0, 461.0, 1344.0, 726.8, 215.0, 1151.0, 32.2, 1822.0, 184.0, 51.2, 307.2, 824.0, 1001.4]\n", "--- 調整イテレーション 2 ---\n", "  今回の調整量: [298.0, 163.0, 366.0, 307.2, 94.0, 527.0, 12.8, 670.0, 84.0, 16.799999999999997, 100.80000000000001, 222.0, 281.6]\n", "  更新後の初期在庫量: [780.0, 298.0, 978.0, 419.59999999999997, 121.0, 624.0, 19.400000000000002, 1152.0, 100.0, 34.400000000000006, 206.39999999999998, 602.0, 719.8]\n", "--- 調整イテレーション 3 ---\n", "  今回の調整量: [298.0, 163.0, 366.0, 307.2, 94.0, 527.0, 12.8, 670.0, 84.0, 16.8, 100.8, 222.0, 281.6]\n", "  更新後の初期在庫量: [482.0, 135.0, 612.0, 112.39999999999998, 27.0, 97.0, 6.600000000000001, 482.0, 16.0, 17.600000000000005, 105.59999999999998, 380.0, 438.19999999999993]\n", "--- 最大反復回数に到達しました。---\n", "=== 混合整数計画法 スケジューリング ===\n", "Restricted license - for non-production use only - expires 2026-11-23\n", "Set parameter TimeLimit to value 500\n", "Gurobi Optimizer version 12.0.2 build v12.0.2rc0 (mac64[arm] - Darwin 24.6.0 24G90)\n", "\n", "CPU model: Apple M1\n", "Thread count: 8 physical cores, 8 logical processors, using up to 8 threads\n", "\n", "Non-default parameters:\n", "TimeLimit  500\n", "\n", "Optimize a model with 620 rows, 1080 columns and 2454 nonzeros\n", "Model fingerprint: 0x69dd2748\n", "Variable types: 560 continuous, 520 integer (0 binary)\n", "Coefficient statistics:\n", "  Matrix range     [3e-01, 1e+06]\n", "  Objective range  [2e+01, 5e+02]\n", "  Bounds range     [1e+00, 1e+00]\n", "  RHS range        [8e-01, 1e+03]\n", "Found heuristic solution: objective 3.013084e+08\n", "Presolve removed 86 rows and 45 columns\n", "Presolve time: 0.03s\n", "Presolved: 534 rows, 1035 columns, 2297 nonzeros\n", "Variable types: 515 continuous, 520 integer (260 binary)\n", "\n", "Root relaxation: objective 3.499265e+04, 531 iterations, 0.01 seconds (0.00 work units)\n", "\n", "    Nodes    |    Current Node    |     Objective Bounds      |     Work\n", " Expl Unexpl |  Obj  Depth IntInf | Incumbent    BestBd   Gap | It/Node Time\n", "\n", "     0     0 34992.6523    0  350 3.0131e+08 34992.6523   100%     -    0s\n", "H    0     0                    9041599.4665 34992.6523   100%     -    0s\n", "     0     0 430289.240    0  212 9041599.47 430289.240  95.2%     -    0s\n", "H    0     0                    4922258.9298 431134.302  91.2%     -    0s\n", "H    0     0                    4921058.9298 431134.302  91.2%     -    0s\n", "     0     0 471266.985    0  290 4921058.93 471266.985  90.4%     -    0s\n", "     0     0 473057.680    0  274 4921058.93 473057.680  90.4%     -    0s\n", "     0     0 473404.101    0  270 4921058.93 473404.101  90.4%     -    0s\n", "     0     0 485194.479    0  267 4921058.93 485194.479  90.1%     -    0s\n", "     0     0 487754.925    0  249 4921058.93 487754.925  90.1%     -    0s\n", "     0     0 488105.866    0  253 4921058.93 488105.866  90.1%     -    0s\n", "     0     0 490679.278    0  248 4921058.93 490679.278  90.0%     -    0s\n", "     0     0 490691.058    0  248 4921058.93 490691.058  90.0%     -    0s\n", "H    0     0                    4812241.2793 490691.580  89.8%     -    0s\n", "H    0     0                    4780290.9433 490691.580  89.7%     -    0s\n", "H    0     0                    4780283.1617 490691.580  89.7%     -    0s\n", "H    0     0                    4777653.9373 490691.580  89.7%     -    0s\n", "H    0     0                    4777551.2708 490691.580  89.7%     -    0s\n", "     0     2 490691.580    0  248 4777551.27 490691.580  89.7%     -    0s\n", "H   26    64                    4701397.2708 493463.178  89.5%  38.9    0s\n", "H   41    64                    4521779.6087 493463.178  89.1%  32.2    0s\n", "H  103   144                    4421581.2768 493463.178  88.8%  22.7    0s\n", "H  123   144                    4416664.3417 493463.178  88.8%  22.6    0s\n", "H  232   252                    3540726.8065 493463.178  86.1%  19.5    0s\n", "H  251   308                    3534021.8065 493463.178  86.0%  18.9    0s\n", "H 1343  1474                    3168806.2473 493463.178  84.4%   9.2    0s\n", "H 1408  1474                    2959460.7081 493463.178  83.3%   9.0    0s\n", "H 2544  2944                    575613.91413 493463.178  14.3%   6.5    1s\n", "H 2577  2944                    568102.49356 493463.178  13.1%   6.4    1s\n", "H 2736  2943                    561835.18332 493463.178  12.2%   6.2    1s\n", "H 2832  2925                    550638.45669 493463.178  10.4%   6.0    1s\n", "H 7639  6014                    548104.60886 493463.178  10.0%   3.5    1s\n", "H 7651  5713                    546543.48858 495148.445  9.40%   3.5    1s\n", "H 7657  5430                    541593.81540 496516.256  8.32%   3.5    2s\n", "H 7660  5160                    541587.95621 496604.840  8.31%   3.5    2s\n", "H 7660  4902                    540690.95621 496604.840  8.15%   3.5    2s\n", "H 7662  4658                    540067.50155 496697.046  8.03%   3.5    2s\n", "H 7662  4425                    539308.95621 496697.046  7.90%   3.5    2s\n", "H 7665  4205                    536086.58541 496962.324  7.30%   3.5    2s\n", "H 7668  3997                    535105.49475 497029.721  7.12%   3.5    3s\n", "H 7668  3796                    535075.49475 497029.721  7.11%   3.5    3s\n", "H 7669  3606                    535034.58541 497051.480  7.10%   3.5    3s\n", "H 7669  3425                    534519.58542 497051.480  7.01%   3.5    3s\n", "H 7669  3253                    534313.58542 497051.480  6.97%   3.5    3s\n", "H 7670  3091                    533373.58538 497056.476  6.81%   3.5    3s\n", "H 7670  2936                    531399.11272 497056.476  6.46%   3.5    3s\n", "H 7670  2789                    531384.11272 497056.476  6.46%   3.5    3s\n", "H 7670  2649                    530869.11272 497056.476  6.37%   3.5    3s\n", "H 7671  2517                    525116.43552 497068.667  5.34%   3.5    3s\n", "H 7674  2394                    523173.80631 497073.197  4.99%   3.7    4s\n", "H 7703  2308                    523113.80631 497073.197  4.98%   3.9    4s\n", "H 7740  2216                    523098.80631 497073.197  4.98%   4.1    4s\n", "H 7742  2106                    522893.75631 497073.197  4.94%   4.1    4s\n", "H 7749  2001                    522873.80631 497073.197  4.93%   4.1    4s\n", "H 7780  1934                    522763.50184 497073.197  4.91%   4.3    4s\n", "H 7936  1938                    522718.50184 497073.197  4.91%   4.9    4s\n", "H 7941  1849                    522598.50184 497073.197  4.88%   4.9    4s\n", "  8016  1904 498524.703   40  165 522598.502 497073.197  4.88%   5.2    5s\n", "H 9007  2438                    522583.50184 497073.197  4.88%   7.6    5s\n", "H 9066  2404                    522568.50184 497073.197  4.88%   7.7    6s\n", "H10086  2676                    522388.44538 497073.197  4.85%   8.4    6s\n", "H11671  3477                    522358.44538 497073.197  4.84%   8.7    6s\n", "H14327  4283                    516799.07068 497073.197  3.82%   8.5    7s\n", "H14424  4194                    515896.76621 497073.197  3.65%   8.4    7s\n", "H14940  4130                    514920.02692 497073.197  3.47%   8.6    7s\n", "H15195  4373                    514830.02692 497073.197  3.45%   8.6    7s\n", "H16394  5341                    514785.02682 497073.197  3.44%   9.1    8s\n", "H16395  5323                    514755.02692 497073.197  3.43%   9.1    8s\n", "H16427  5157                    514669.36992 497073.197  3.42%   9.1    8s\n", "H19758  7814                    514609.36992 497087.616  3.40%  10.4    9s\n", " 21148  9025 512302.429  517   36 514609.370 497138.204  3.40%  10.5   10s\n", "H21172  9108                    514545.10059 497138.204  3.38%  10.5   10s\n", "H21173  9087                    514534.36992 497138.204  3.38%  10.5   10s\n", "H21189  8694                    514327.52229 497138.204  3.34%  10.5   10s\n", "H21317  8561                    514196.55497 497138.204  3.32%  10.7   10s\n", "H21993  9221                    514196.42528 497138.204  3.32%  10.9   10s\n", "H22702  9744                    512825.99985 497138.479  3.06%  10.9   11s\n", "H22705  9742                    512822.21003 497138.479  3.06%  10.9   11s\n", "H24054  9869                    512575.58082 497166.144  3.01%  11.1   11s\n", "H29002 13714                    512564.53474 497236.439  2.99%  12.0   17s\n", "H29002 13490                    512389.22233 497236.439  2.96%  12.0   17s\n", " 29019 13502 508925.196   91  280 512389.222 497236.439  2.96%  12.0   20s\n", "H29021 12827                    512359.22232 497236.439  2.95%  12.0   20s\n", "H29024 12187                    512299.22233 497236.439  2.94%  12.0   21s\n", "H29028 11580                    512074.22233 497236.439  2.90%  12.0   22s\n", " 29035 11584 503622.592   54  289 512074.222 497236.439  2.90%  12.0   25s\n", "H29037 11006                    512058.93647 497236.439  2.89%  12.0   26s\n", "H29134 10531                    512028.93647 497236.439  2.89%  12.3   26s\n", "H29135 10008                    511818.93647 497236.439  2.85%  12.3   26s\n", "H29255  9585                    511788.93647 497236.439  2.84%  12.5   27s\n", "H29531  9288                    511758.93647 497236.439  2.84%  12.9   28s\n", "H30028  9086                    511754.56839 497236.439  2.84%  13.5   29s\n", " 30506  9395 508807.070  123   98 511754.568 497236.439  2.84%  13.8   30s\n", " 37210 12665 501389.061   59  155 511754.568 497328.491  2.82%  16.5   35s\n", "H40407 14304                    511753.15455 497392.915  2.81%  17.0   37s\n", "H41012 14005                    511738.15455 497410.906  2.80%  17.1   38s\n", "*42198 13898             391    511648.15455 497434.143  2.78%  17.2   38s\n", "H44765 15050                    511648.06280 497463.457  2.77%  17.8   39s\n", "H44912 14654                    511647.98339 497482.709  2.77%  17.8   39s\n", " 45149 15406 501220.931   44  189 511647.983 497482.709  2.77%  17.9   40s\n", " 57086 23497 498031.307   38  166 511647.983 497623.950  2.74%  19.3   45s\n", " 67530 32198 501727.964   53  166 511647.983 497709.484  2.72%  20.3   50s\n", " 79502 41372 505970.314   55  188 511647.983 497785.263  2.71%  21.0   55s\n", " 89431 49098 500442.257   52  163 511647.983 497840.388  2.70%  21.5   60s\n", " 97778 55927 498926.627   44  192 511647.983 497879.367  2.69%  22.1   65s\n", " 108612 64456 500160.996   51  189 511647.983 497925.687  2.68%  22.5   70s\n", " 117959 71895 508581.827  121  126 511647.983 497960.161  2.68%  22.8   75s\n", " 128747 80632     cutoff   75      511647.983 498003.039  2.67%  23.3   80s\n", " 140090 89386 500917.636   54  201 511647.983 498030.053  2.66%  23.6   85s\n", " 148592 96341 502269.652   63  156 511647.983 498063.705  2.66%  23.7   90s\n", " 160901 105516 510741.083   74  124 511647.983 498096.505  2.65%  23.8   95s\n", " 167688 110510 508160.044   96  143 511647.983 498111.606  2.65%  23.9  100s\n", " 177749 118761 503931.854   67  143 511647.983 498132.596  2.64%  24.1  105s\n", " 190316 128762 511026.569   57  177 511647.983 498160.186  2.64%  24.1  110s\n", " 201684 137958 503172.210   66  176 511647.983 498187.400  2.63%  24.1  115s\n", " 212949 146974 507086.044   88  191 511647.983 498209.335  2.63%  24.1  120s\n", " 221340 153185 509038.949  113  107 511647.983 498226.997  2.62%  24.2  125s\n", " 232390 162111 502032.334   60  164 511647.983 498248.041  2.62%  24.2  130s\n", " 244493 171350 500188.339   50  161 511647.983 498267.203  2.62%  24.1  135s\n", " 255803 179908 507675.386   65  158 511647.983 498288.545  2.61%  24.2  140s\n", " 267053 188558 504835.268   78  139 511647.983 498305.435  2.61%  24.4  145s\n", " 280968 199886 499021.752   38  225 511647.983 498326.154  2.60%  24.3  150s\n", " 292676 208798 499374.542   40  200 511647.983 498339.156  2.60%  24.4  155s\n", " 304723 218042 500301.490   41  155 511647.983 498354.024  2.60%  24.5  160s\n", " 317498 228277 505282.362   78  153 511647.983 498366.590  2.60%  24.5  165s\n", " 329694 238164 499948.838   46  168 511647.983 498384.307  2.59%  24.5  170s\n", " 342686 248374 501634.764   48  174 511647.983 498401.234  2.59%  24.5  175s\n", " 355297 258060 499162.452   49  199 511647.983 498414.809  2.59%  24.5  180s\n", " 366940 267359 504064.490   63  182 511647.983 498430.037  2.58%  24.5  185s\n", " 379194 276821 505278.998   70  143 511647.983 498442.950  2.58%  24.5  190s\n", " 389892 285228 503101.161   71  134 511647.983 498454.602  2.58%  24.5  195s\n", " 403342 295870 508524.000   71  154 511647.983 498466.553  2.58%  24.5  200s\n", " 414687 304565 502476.638   58  149 511647.983 498478.635  2.57%  24.5  205s\n", " 427379 314696 500636.915   43  202 511647.983 498488.322  2.57%  24.6  210s\n", " 439179 323895 505186.599   51  166 511647.983 498499.033  2.57%  24.6  215s\n", " 450213 332315 499052.565   36  234 511647.983 498509.587  2.57%  24.7  220s\n", " 463221 342651 508037.003   61  171 511647.983 498519.306  2.57%  24.6  225s\n", " 475975 352524     cutoff   76      511647.983 498530.040  2.56%  24.6  230s\n", " 488074 362070 510777.475  112  101 511647.983 498538.055  2.56%  24.6  235s\n", " 500138 371407 499434.019   44  188 511647.983 498548.160  2.56%  24.7  240s\n", " 512064 380880 500244.617   55  191 511647.983 498559.301  2.56%  24.7  245s\n", " 524558 390633 510898.186   62  136 511647.983 498568.417  2.56%  24.6  250s\n", " 534755 398961 501661.266   61  175 511647.983 498576.265  2.55%  24.7  255s\n", " 546189 407332 501453.186   58  152 511647.983 498583.746  2.55%  24.8  260s\n", " 558261 416891 510189.538  110  105 511647.983 498593.023  2.55%  24.7  265s\n", " 571376 427349 499536.830   56  138 511647.983 498600.484  2.55%  24.7  270s\n", " 582898 436517 503042.848   55  189 511647.983 498608.910  2.55%  24.7  275s\n", " 595715 446568 500776.295   48  169 511647.983 498615.901  2.55%  24.7  280s\n", " 607408 455737 501810.716   49  153 511647.983 498624.933  2.55%  24.7  285s\n", "H609869 457042                    511642.55652 498627.013  2.54%  24.7  287s\n", " 614942 461392 509581.500   61  134 511642.557 498630.563  2.54%  24.7  290s\n", " 625913 469731 499518.624   43  173 511642.557 498638.682  2.54%  24.8  295s\n", "H630313 472922                    511632.98338 498641.623  2.54%  24.8  297s\n", "H630375 472661                    511617.98339 498641.623  2.54%  24.8  297s\n", "H630614 471398                    511557.98339 498641.623  2.52%  24.8  297s\n", " 636986 476786 503058.592   63  157 511557.983 498646.500  2.52%  24.8  300s\n", " 649000 486210 502563.092   51  188 511557.983 498653.291  2.52%  24.8  305s\n", " 660423 495149 499879.516   42  195 511557.983 498661.582  2.52%  24.8  310s\n", " 672428 504439 507219.788   56  159 511557.983 498668.848  2.52%  24.8  315s\n", " 682982 512122 502536.535   52  137 511557.983 498675.278  2.52%  24.9  320s\n", " 693696 520127 505433.507   56  151 511557.983 498681.583  2.52%  25.0  325s\n", " 701267 526093 501133.682   43  165 511557.983 498684.249  2.52%  25.0  330s\n", " 709820 532505 502577.819   54  166 511557.983 498686.034  2.52%  25.0  335s\n", " 720532 540531 500449.200   52  187 511557.983 498692.470  2.51%  25.1  340s\n", " 731120 548687 499971.481   41  177 511557.983 498699.190  2.51%  25.1  345s\n", " 743820 559086 infeasible   64      511557.983 498705.557  2.51%  25.1  350s\n", "H748631 535112                    510777.98339 498707.795  2.36%  25.1  354s\n", " 748662 535120 500861.001   41  189 510777.983 498707.795  2.36%  25.1  355s\n", "H749247 535081                    510762.98339 498708.472  2.36%  25.1  356s\n", " 757610 542021 501007.046   52  201 510762.983 498713.392  2.36%  25.1  360s\n", " 768060 550143 501058.440   47  203 510762.983 498719.589  2.36%  25.1  365s\n", " 779969 559252 502167.759   58  155 510762.983 498728.019  2.36%  25.1  370s\n", " 790819 567468     cutoff  113      510762.983 498733.367  2.36%  25.1  375s\n", " 802018 576257 501517.680   55  176 510762.983 498740.073  2.35%  25.1  380s\n", " 813279 584838 500211.595   40  189 510762.983 498746.013  2.35%  25.1  385s\n", "H814938 584684                    510732.98339 498747.434  2.35%  25.1  385s\n", "H815009 582170                    510672.98339 498747.466  2.34%  25.1  385s\n", " 823716 588994 505079.863   66  152 510672.983 498751.332  2.33%  25.2  390s\n", " 835166 597370 507810.105  113  115 510672.983 498757.589  2.33%  25.2  395s\n", " 845648 605588 501586.819   50  147 510672.983 498762.785  2.33%  25.2  400s\n", " 857113 614334 501896.285   39  160 510672.983 498769.102  2.33%  25.2  405s\n", " 868228 622791 505827.114  109  123 510672.983 498774.697  2.33%  25.2  410s\n", " 878242 630425 506336.187  110  111 510672.983 498780.825  2.33%  25.2  415s\n", " 890386 639427 499297.372   43  206 510672.983 498787.324  2.33%  25.2  420s\n", " 901416 647753 506177.079   60  149 510672.983 498793.595  2.33%  25.2  425s\n", "H907356 650984                    510653.64039 498797.741  2.32%  25.2  427s\n", "H909616 652122                    510638.81155 498799.473  2.32%  25.2  429s\n", " 910851 653409 510010.665   69  158 510638.812 498800.276  2.32%  25.2  430s\n", " 922303 662155 502653.867   49  159 510638.812 498806.198  2.32%  25.3  435s\n", " 933986 670823 507297.346   72  140 510638.812 498811.624  2.32%  25.3  440s\n", " 944692 679141 501027.632   47  210 510638.812 498816.395  2.32%  25.3  445s\n", " 955925 687779 501147.556   47  142 510638.812 498821.815  2.31%  25.3  450s\n", " 967490 696506 504710.442   61  175 510638.812 498826.397  2.31%  25.3  455s\n", " 975221 702168 505983.750   72  157 510638.812 498829.736  2.31%  25.3  460s\n", "H975226 701448                    510623.81155 498829.736  2.31%  25.3  460s\n", "H975227 700743                    510608.81155 498829.736  2.31%  25.3  460s\n", "H976549 697706                    510523.15455 498830.023  2.29%  25.3  464s\n", " 978651 699753 503046.777   71  157 510523.155 498830.752  2.29%  25.3  465s\n", " 989253 707821 500307.836   45  168 510523.155 498834.941  2.29%  25.3  470s\n", " 1000433 716569 506022.534   83  124 510523.155 498839.748  2.29%  25.3  475s\n", " 1011718 725014 499911.131   44  163 510523.155 498845.251  2.29%  25.3  480s\n", " 1022986 733269 507822.817   83  122 510523.155 498851.502  2.29%  25.3  485s\n", " 1034096 741918 509667.450   92  109 510523.155 498855.224  2.29%  25.3  490s\n", " 1045366 750464 499095.865   41  179 510523.155 498861.122  2.28%  25.4  495s\n", "H1047818 750410                    510493.15455 498862.621  2.28%  25.4  496s\n", " 1055523 756220 507333.519  108  118 510493.155 498866.492  2.28%  25.4  500s\n", "\n", "Cutting planes:\n", "  Learned: 1\n", "  Gomory: 25\n", "  Implied bound: 77\n", "  MIR: 274\n", "  Flow cover: 425\n", "  Flow path: 336\n", "  Inf proof: 1\n", "  Relax-and-lift: 1\n", "\n", "Explored 1055571 nodes (26762134 simplex iterations) in 500.02 seconds (473.62 work units)\n", "Thread count was 8 (of 8 available processors)\n", "\n", "Solution count 10: 510493 510523 510609 ... 510778\n", "\n", "Time limit reached\n", "Best objective 5.104931545544e+05, best bound 4.988664924812e+05, gap 2.2775%\n", "Gurobi status= 9\n", "ステータス: Not Solved\n", "時間制限により最適解は見つかりませんでしたが、実行可能解を取得しました\n", "総コスト: 510493.1545543641\n", "\n", "=== 最適化結果 ===\n", "最良個体の総コスト: 510493.15\n", "計算時間: 501.06秒\n", "結果をCSVファイルに保存: result//MIP_results_D28.csv\n", "\n", "=== 結果のプロット ===\n", "プロットを保存: result//MIP_results_D28.png\n", "時間制約違反: 0 期間\n", "\n", "=== Processing data/D42.csv ===\n", "\n", "=== 初期在庫水準の調整アルゴリズム開始 ===\n", "--- 調整イテレーション 1 ---\n", "  今回の調整量: [121.14999999999998, 240.0, 60.0, 75.0, 198.0, 57.599999999999994, 124.60000000000002, 411.04999999999995, 105.19999999999999, 54.69999999999999, 806.4000000000001, 201.60000000000002, 222.0]\n", "  更新後の初期在庫量: [392.85, 822.0, 216.0, 250.0, 732.0, 213.4, 409.4, 1294.95, 374.8, 218.3, 1949.6, 613.4, 525.0]\n", "--- 調整イテレーション 2 ---\n", "  今回の調整量: [121.14999999999998, 240.0, 60.0, 75.0, 198.0, 57.599999999999994, 124.60000000000002, 411.04999999999995, 105.19999999999999, 54.69999999999999, 806.4000000000001, 201.60000000000002, 222.0]\n", "  更新後の初期在庫量: [271.70000000000005, 582.0, 156.0, 175.0, 534.0, 155.8, 284.79999999999995, 883.9000000000001, 269.6, 163.60000000000002, 1143.1999999999998, 411.79999999999995, 303.0]\n", "--- 調整イテレーション 3 ---\n", "  今回の調整量: [121.15, 240.0, 60.0, 75.0, 198.0, 57.599999999999994, 124.6, 411.05, 105.19999999999999, 54.7, 806.4, 201.6, 222.0]\n", "  更新後の初期在庫量: [150.55000000000004, 342.0, 96.0, 100.0, 336.0, 98.20000000000002, 160.19999999999996, 472.8500000000001, 164.40000000000003, 108.90000000000002, 336.79999999999984, 210.19999999999996, 81.0]\n", "--- 最大反復回数に到達しました。---\n", "=== 混合整数計画法 スケジューリング ===\n", "Set parameter TimeLimit to value 500\n", "Gurobi Optimizer version 12.0.2 build v12.0.2rc0 (mac64[arm] - Darwin 24.6.0 24G90)\n", "\n", "CPU model: Apple M1\n", "Thread count: 8 physical cores, 8 logical processors, using up to 8 threads\n", "\n", "Non-default parameters:\n", "TimeLimit  500\n", "\n", "Optimize a model with 620 rows, 1080 columns and 2454 nonzeros\n", "Model fingerprint: 0xa88ffaf7\n", "Variable types: 560 continuous, 520 integer (0 binary)\n", "Coefficient statistics:\n", "  Matrix range     [3e-01, 1e+06]\n", "  Objective range  [2e+01, 5e+02]\n", "  Bounds range     [1e+00, 1e+00]\n", "  RHS range        [9e+00, 1e+03]\n", "Found heuristic solution: objective 2.548496e+08\n", "Presolve removed 91 rows and 55 columns\n", "Presolve time: 0.01s\n", "Presolved: 529 rows, 1025 columns, 2277 nonzeros\n", "Variable types: 505 continuous, 520 integer (260 binary)\n", "\n", "Root relaxation: objective 1.434982e+04, 498 iterations, 0.00 seconds (0.00 work units)\n", "\n", "    Nodes    |    Current Node    |     Objective Bounds      |     Work\n", " Expl Unexpl |  Obj  Depth IntInf | Incumbent    BestBd   Gap | It/Node Time\n", "\n", "     0     0 14349.8224    0  398 2.5485e+08 14349.8224   100%     -    0s\n", "H    0     0                    7239376.8939 14349.8224   100%     -    0s\n", "H    0     0                    7237776.8939 14349.8224   100%     -    0s\n", "     0     0 291116.120    0  311 7237776.89 291116.120  96.0%     -    0s\n", "H    0     0                    1757654.3565 291609.508  83.4%     -    0s\n", "H    0     0                    1757239.3565 291609.508  83.4%     -    0s\n", "     0     0 349744.187    0  331 1757239.36 349744.187  80.1%     -    0s\n", "     0     0 352161.592    0  295 1757239.36 352161.592  80.0%     -    0s\n", "     0     0 352165.488    0  297 1757239.36 352165.488  80.0%     -    0s\n", "     0     0 357893.351    0  287 1757239.36 357893.351  79.6%     -    0s\n", "     0     0 358233.923    0  273 1757239.36 358233.923  79.6%     -    0s\n", "     0     0 358504.040    0  281 1757239.36 358504.040  79.6%     -    0s\n", "     0     0 358504.320    0  265 1757239.36 358504.320  79.6%     -    0s\n", "H    0     0                    1756854.3565 358525.381  79.6%     -    0s\n", "H    0     0                    1756834.3836 358525.381  79.6%     -    0s\n", "H    0     0                    1756693.7580 358525.381  79.6%     -    0s\n", "H    0     0                    1756678.7580 358525.381  79.6%     -    0s\n", "H    0     2                    1633023.6191 358525.381  78.0%     -    0s\n", "     0     2 358525.381    0  265 1633023.62 358525.381  78.0%     -    0s\n", "H   26    64                    1576589.5711 358571.117  77.3%   8.3    0s\n", "H   31    64                    1574445.2538 358571.117  77.2%   8.5    0s\n", "H   46    64                    1438096.5059 358571.117  75.1%  10.1    0s\n", "H   56    64                    1412172.6439 358571.117  74.6%   9.9    0s\n", "H  255   280                    1179886.4420 358571.117  69.6%   7.8    0s\n", "H  279   336                    1179811.4420 358571.117  69.6%   7.6    0s\n", "H  286   336                    1179636.5773 358571.117  69.6%   7.5    0s\n", "H  300   336                    1177640.7429 358571.117  69.6%   7.3    0s\n", "H  307   336                    1149388.9132 358571.117  68.8%   7.3    0s\n", "H  328   336                    1147185.6920 358571.117  68.7%   7.1    0s\n", "H  335   408                    1147110.6920 358571.117  68.7%   7.1    0s\n", "H  745   864                    1060238.2318 358571.117  66.2%   5.0    0s\n", "H  764   864                    830388.81668 358571.117  56.8%   4.9    0s\n", "H  783   864                    715683.20194 358571.117  49.9%   4.8    0s\n", "H  799   864                    412637.14012 358571.117  13.1%   4.7    0s\n", "H  836   864                    399944.26376 358571.117  10.3%   4.6    0s\n", "H  863  1040                    399929.26376 358571.117  10.3%   4.5    0s\n", "H 1503  1808                    387759.56510 358571.117  7.53%   3.3    0s\n", "H 1655  1808                    383719.20690 358571.117  6.55%   3.1    0s\n", "H 1731  1808                    383324.95161 358571.117  6.46%   3.1    0s\n", "H 1769  1807                    382515.94462 358571.117  6.26%   3.0    0s\n", "H 1807  2174                    382500.94462 358571.117  6.26%   3.0    0s\n", "H 2610  2813                    379462.62112 358571.117  5.51%   2.6    0s\n", "H 2618  2771                    369906.25383 358571.117  3.06%   2.6    0s\n", "H 2866  3139                    369506.17220 358571.117  2.96%   2.5    0s\n", "H 3697  3960                    368936.32266 358571.117  2.81%   2.3    1s\n", "H 4582  4597                    367655.40190 358571.117  2.47%   2.1    1s\n", "H 4747  4226                    366327.52556 358571.117  2.12%   2.1    1s\n", "H 4875  3310                    365253.30160 358591.662  1.82%   2.1    1s\n", "H 5983  4231                    365238.30160 358606.089  1.82%   2.4    1s\n", "H 6053  4219                    365223.30160 358606.089  1.81%   2.4    1s\n", "H 6096  4186                    365193.30160 358606.089  1.80%   2.4    1s\n", "H 6848  4592                    365178.30160 359197.047  1.64%   2.4    1s\n", "H 6853  4365                    364622.30160 359368.563  1.44%   2.4    2s\n", "H 6853  4146                    364534.30160 359368.563  1.42%   2.4    2s\n", "H 6867  3947                    363769.33118 359524.676  1.17%   2.4    2s\n", "H 6868  3750                    363715.73189 359526.723  1.15%   2.4    2s\n", "H 6868  3562                    363700.73189 359526.723  1.15%   2.4    2s\n", "H 6871  3385                    363670.73189 359529.003  1.14%   2.4    2s\n", "H 6875  3218                    362945.39472 359538.145  0.94%   2.4    2s\n", "H 6875  3057                    362930.39472 359538.145  0.93%   2.4    2s\n", "H 6877  2904                    361867.89472 359555.105  0.64%   2.4    3s\n", "H 6877  2758                    361487.99718 359555.105  0.53%   2.4    3s\n", "H 6879  2620                    361472.99718 359556.905  0.53%   2.4    3s\n", "H 6884  2492                    361386.59206 359581.035  0.50%   2.4    3s\n", "H 6884  2367                    361371.59206 359581.035  0.50%   2.4    3s\n", "H 6888  2250                    361345.37940 359595.200  0.48%   2.4    4s\n", "H 6888  2136                    361272.37940 359595.200  0.46%   2.4    4s\n", "H 6889  2030                    361191.55615 359602.570  0.44%   2.4    4s\n", "H 6891  1928                    361176.55615 359602.994  0.44%   2.4    4s\n", "H 6891  1831                    361168.05615 359602.994  0.43%   2.4    4s\n", "H 6900  1744                    361067.05615 359616.526  0.40%   2.4    4s\n", "  6902  1746 361067.056   83  244 361067.056 359617.831  0.40%   2.4    5s\n", "H 6904  1658                    361049.68136 359619.485  0.40%   2.4    5s\n", "H 6904  1574                    360981.63336 359619.485  0.38%   2.4    5s\n", "H 6924  1509                    360951.63336 359630.966  0.37%   2.7    6s\n", "H 6926  1434                    360933.93136 359635.821  0.36%   2.7    6s\n", "H 6926  1362                    360895.88336 359635.821  0.35%   2.7    6s\n", "H 6929  1296                    360880.88336 359637.926  0.34%   2.7    6s\n", "H 6929  1230                    360865.88336 359637.926  0.34%   2.7    6s\n", "H 6946  1179                    360835.88336 359665.637  0.32%   2.7    8s\n", "H 6981  1164                    360820.88335 359667.935  0.32%   3.1    8s\n", "H 6985  1106                    360765.16936 359667.935  0.30%   3.1    8s\n", "H 7042  1097                    360690.07007 359667.935  0.28%   3.2    8s\n", "H 7282  1120                    360646.39332 359667.935  0.27%   3.6    9s\n", "H 7284  1071                    360646.09621 359667.935  0.27%   3.6    9s\n", "H 8314  1315                    360631.09622 359694.218  0.26%   4.3    9s\n", "  8837  1547 360245.586   57  154 360631.096 359699.893  0.26%   4.5   10s\n", "H21389  8184                    360629.77297 359820.208  0.22%   7.7   13s\n", "H22476  8672                    360601.09620 359822.333  0.22%   7.9   13s\n", "H22683  8512                    360586.09621 359822.333  0.21%   7.9   13s\n", "H22955  8574                    360584.77294 359823.013  0.21%   7.9   14s\n", "H22971  8190                    360554.77296 359823.013  0.20%   7.9   14s\n", " 25867  9882 359948.281   37  210 360554.773 359834.956  0.20%   8.3   15s\n", " 29518 11148 360156.668   41  230 360554.773 359848.939  0.20%   8.7   20s\n", " 31705 11745 360548.600  105   86 360554.773 359848.939  0.20%   8.9   25s\n", " 49146 12636     cutoff   53      360554.773 360020.202  0.15%  10.3   30s\n", " 70875 13281 360334.570   46  197 360554.773 360118.582  0.12%  11.2   35s\n", "*83984 15726             171    360552.12617 360165.113  0.11%  11.5   37s\n", " 87839 16218 360401.425   50  200 360552.126 360182.032  0.10%  11.5   40s\n", " 111763 19845     cutoff   51      360552.126 360255.651  0.08%  11.6   45s\n", " 140707 27373 360475.901  104   63 360552.126 360300.707  0.07%  11.1   50s\n", " 171371 35977 360475.601   47  234 360552.126 360330.874  0.06%  10.4   55s\n", " 204263 43778 360478.850   59  217 360552.126 360357.451  0.05%   9.9   60s\n", " 238492 51126 360527.408   81   96 360552.126 360380.437  0.05%   9.5   65s\n", " 270546 56516 360499.399   91   82 360552.126 360401.763  0.04%   9.2   70s\n", " 303394 60633     cutoff  124      360552.126 360423.043  0.04%   8.9   75s\n", " 335585 64182 360515.799  126   35 360552.126 360440.611  0.03%   8.8   80s\n", " 369822 68492 360540.506  108   53 360552.126 360455.236  0.03%   8.5   85s\n", " 403659 73144     cutoff  145      360552.126 360467.814  0.02%   8.3   90s\n", " 436550 79336     cutoff  113      360552.126 360477.822  0.02%   8.1   95s\n", " 471038 87218     cutoff   87      360552.126 360484.744  0.02%   7.9  100s\n", " 505407 93481 360533.384  150   11 360552.126 360490.698  0.02%   7.8  105s\n", " 535146 98041     cutoff  108      360552.126 360494.867  0.02%   7.6  110s\n", " 566948 101751 360535.488  138   14 360552.126 360499.484  0.01%   7.5  115s\n", " 600280 105729 360539.980  120   29 360552.126 360503.558  0.01%   7.4  120s\n", " 633889 109121 360521.440  134    9 360552.126 360507.606  0.01%   7.2  125s\n", " 659320 110881 360549.227  148   10 360552.126 360510.355  0.01%   7.2  130s\n", " 685901 113155 360534.934  138    9 360552.126 360512.726  0.01%   7.1  135s\n", " 709109 114816 360535.895  132   28 360552.126 360514.552  0.01%   7.0  140s\n", " 731493 116531 360534.505  143    8 360552.126 360516.064  0.01%   6.9  145s\n", "\n", "Cutting planes:\n", "  Gomory: 52\n", "  Cover: 30\n", "  Implied bound: 23\n", "  MIR: 376\n", "  Flow cover: 316\n", "  Flow path: 60\n", "  Inf proof: 42\n", "  Network: 4\n", "  RLT: 1\n", "  Relax-and-lift: 4\n", "\n", "Explored 732412 nodes (5058020 simplex iterations) in 145.17 seconds (92.81 work units)\n", "Thread count was 8 (of 8 available processors)\n", "\n", "Solution count 10: 360552 360555 360585 ... 360690\n", "\n", "Optimal solution found (tolerance 1.00e-04)\n", "Best objective 3.605521261667e+05, best bound 3.605161278635e+05, gap 0.0100%\n", "Gurobi status= 2\n", "ステータス: Optimal\n", "総コスト: 360552.12616666674\n", "\n", "=== 最適化結果 ===\n", "最良個体の総コスト: 360552.13\n", "計算時間: 145.92秒\n", "結果をCSVファイルに保存: result//MIP_results_D42.csv\n", "\n", "=== 結果のプロット ===\n", "プロットを保存: result//MIP_results_D42.png\n", "時間制約違反: 0 期間\n", "\n", "=== Processing data/D40.csv ===\n", "\n", "=== 初期在庫水準の調整アルゴリズム開始 ===\n", "--- 調整イテレーション 1 ---\n", "  今回の調整量: [1599.8999999999996, 30.0, 336.0, 22.950000000000003, 194.39999999999998, 335.29999999999995, 109.85000000000002, 32.2, 117.0]\n", "  更新後の初期在庫量: [4906.1, 73.0, 1304.0, 89.05, 629.6, 861.7, 413.15, 82.8, 272.0]\n", "--- 調整イテレーション 2 ---\n", "  今回の調整量: [1599.9, 30.0, 336.0, 22.950000000000003, 194.39999999999998, 335.29999999999995, 109.85000000000002, 32.2, 117.0]\n", "  更新後の初期在庫量: [3306.2000000000003, 43.0, 968.0, 66.1, 435.20000000000005, 526.4000000000001, 303.29999999999995, 50.599999999999994, 155.0]\n", "--- 調整イテレーション 3 ---\n", "  今回の調整量: [1599.9, 30.0, 336.0, 22.950000000000003, 194.4, 335.3, 109.85, 32.2, 117.0]\n", "  更新後の初期在庫量: [1706.3000000000002, 13.0, 632.0, 43.14999999999999, 240.80000000000004, 191.10000000000008, 193.44999999999996, 18.39999999999999, 38.0]\n", "--- 最大反復回数に到達しました。---\n", "=== 混合整数計画法 スケジューリング ===\n", "Set parameter TimeLimit to value 500\n", "Gurobi Optimizer version 12.0.2 build v12.0.2rc0 (mac64[arm] - Darwin 24.6.0 24G90)\n", "\n", "CPU model: Apple M1\n", "Thread count: 8 physical cores, 8 logical processors, using up to 8 threads\n", "\n", "Non-default parameters:\n", "TimeLimit  500\n", "\n", "Optimize a model with 460 rows, 760 columns and 1742 nonzeros\n", "Model fingerprint: 0x743ceb2c\n", "Variable types: 400 continuous, 360 integer (0 binary)\n", "Coefficient statistics:\n", "  Matrix range     [3e-01, 1e+06]\n", "  Objective range  [2e+01, 5e+02]\n", "  Bounds range     [1e+00, 1e+00]\n", "  RHS range        [1e+01, 2e+03]\n", "Found heuristic solution: objective 2.611706e+08\n", "Presolve removed 85 rows and 39 columns\n", "Presolve time: 0.01s\n", "Presolved: 375 rows, 721 columns, 1593 nonzeros\n", "Variable types: 361 continuous, 360 integer (180 binary)\n", "\n", "Root relaxation: objective 1.385219e+04, 350 iterations, 0.00 seconds (0.00 work units)\n", "\n", "    Nodes    |    Current Node    |     Objective Bounds      |     Work\n", " Expl Unexpl |  Obj  Depth IntInf | Incumbent    BestBd   Gap | It/Node Time\n", "\n", "     0     0 13852.1860    0  290 2.6117e+08 13852.1860   100%     -    0s\n", "H    0     0                    1864136.2554 13852.1860  99.3%     -    0s\n", "H    0     0                    1863321.2554 13852.1860  99.3%     -    0s\n", "     0     0 146612.449    0  240 1863321.26 146612.449  92.1%     -    0s\n", "H    0     0                    854763.59827 147219.851  82.8%     -    0s\n", "H    0     0                    854363.59827 147219.851  82.8%     -    0s\n", "H    0     0                    484632.95946 147219.851  69.6%     -    0s\n", "     0     0 147219.851    0  239 484632.959 147219.851  69.6%     -    0s\n", "     0     0 180242.417    0  273 484632.959 180242.417  62.8%     -    0s\n", "H    0     0                    327100.76977 180613.391  44.8%     -    0s\n", "     0     0 181301.743    0  271 327100.770 181301.743  44.6%     -    0s\n", "     0     0 181490.162    0  270 327100.770 181490.162  44.5%     -    0s\n", "     0     0 191805.225    0  241 327100.770 191805.225  41.4%     -    0s\n", "     0     0 192995.229    0  220 327100.770 192995.229  41.0%     -    0s\n", "     0     0 193032.249    0  224 327100.770 193032.249  41.0%     -    0s\n", "     0     0 194731.786    0  254 327100.770 194731.786  40.5%     -    0s\n", "H    0     0                    325106.76977 194738.348  40.1%     -    0s\n", "H    0     0                    322190.26977 194738.348  39.6%     -    0s\n", "H    0     0                    320330.10162 194738.348  39.2%     -    0s\n", "H    0     0                    247809.05131 195281.154  21.2%     -    0s\n", "H    0     0                    247764.05131 195281.154  21.2%     -    0s\n", "     0     0 195281.154    0  258 247764.051 195281.154  21.2%     -    0s\n", "     0     0 195563.958    0  250 247764.051 195563.958  21.1%     -    0s\n", "     0     0 195578.802    0  245 247764.051 195578.802  21.1%     -    0s\n", "H    0     0                    241618.05131 195578.802  19.1%     -    0s\n", "H    0     0                    241345.55131 195578.802  19.0%     -    0s\n", "H    0     0                    241330.55131 195578.802  19.0%     -    0s\n", "H    0     0                    241309.05131 195578.802  19.0%     -    0s\n", "H    0     0                    227376.47498 195981.100  13.8%     -    0s\n", "H    0     0                    227316.47498 195981.100  13.8%     -    0s\n", "     0     0 195981.100    0  240 227316.475 195981.100  13.8%     -    0s\n", "H    0     0                    227292.76082 195981.100  13.8%     -    0s\n", "     0     0 196023.628    0  240 227292.761 196023.628  13.8%     -    0s\n", "     0     2 196023.628    0  240 227292.761 196023.628  13.8%     -    0s\n", "H   26    64                    223970.76082 196710.859  12.2%  20.0    0s\n", "H   51    64                    222949.48127 196710.859  11.8%  15.2    0s\n", "H  223   272                    222919.48127 196710.859  11.8%  10.5    0s\n", "H  229   272                    222904.48127 196710.859  11.8%  10.5    0s\n", "H  253   272                    222618.98127 196710.859  11.6%  10.4    0s\n", "H  259   272                    221155.48127 196710.859  11.1%  10.2    0s\n", "H 1463  1424                    207118.01290 196710.859  5.02%   4.0    0s\n", "H 1476  1421                    205989.30094 196710.859  4.50%   3.9    0s\n", "H 1550  1723                    205929.30094 196710.859  4.48%   3.8    0s\n", "H 1591  1641                    203441.32548 196710.859  3.31%   3.8    0s\n", "H 1595  1602                    203189.60278 196710.859  3.19%   3.8    0s\n", "H 1862  1719                    203174.60278 196710.859  3.18%   3.5    0s\n", "H 2240  1835                    202816.59562 196710.859  3.01%   3.4    0s\n", "H 2245  1826                    202791.75187 196710.859  3.00%   3.4    0s\n", "H 2448  2060                    202718.75187 196710.859  2.96%   3.5    1s\n", "H 2500  1963                    202643.75187 196710.859  2.93%   3.5    1s\n", "H 3445  2364                    202634.59562 197562.596  2.50%   3.8    1s\n", "H 3465  2258                    202598.75187 198409.262  2.07%   3.8    2s\n", "H 3467  2146                    201858.31189 198420.525  1.70%   3.8    2s\n", "H 3467  2038                    201843.31189 198420.525  1.70%   3.8    2s\n", "H 3468  1936                    201798.31189 198425.501  1.67%   3.8    2s\n", "H 3468  1839                    201783.31189 198425.501  1.66%   3.8    2s\n", "H 3468  1747                    201594.31189 198425.501  1.57%   3.8    2s\n", "H 3468  1659                    201579.31189 198425.501  1.56%   3.8    2s\n", "H 3472  1577                    201330.06189 198462.756  1.42%   3.8    3s\n", "H 3475  1499                    201300.06189 198584.498  1.35%   3.8    3s\n", "H 3480  1427                    200295.53080 198651.100  0.82%   3.8    4s\n", "H 3480  1355                    200274.99275 198651.100  0.81%   3.8    4s\n", "H 3480  1287                    200244.99275 198651.100  0.80%   3.8    4s\n", "H 3481  1224                    200230.85177 198651.100  0.79%   4.4    4s\n", "  3482  1226 198651.100   15  211 200230.852 198651.100  0.79%   4.5    5s\n", "H 3507  1206                    200195.74275 198651.100  0.77%   4.8    5s\n", "H 3520  1142                    200185.85177 198651.100  0.77%   4.9    5s\n", "H 3525  1085                    200170.85177 198651.100  0.76%   4.9    5s\n", "H 3953  1149                    200169.99275 198651.100  0.76%   6.3    5s\n", "H 3992  1085                    200135.74275 198651.100  0.74%   6.5    5s\n", "H 4240  1084                    200105.74275 198651.100  0.73%   6.9    6s\n", "H 5425  1094                    199836.80456 198721.542  0.56%   6.8    6s\n", "H 6480  1076                    199710.77331 198847.047  0.43%   7.1    7s\n", "H 6512  1030                    199680.77331 198847.047  0.42%   7.1    7s\n", "H 6520   996                    199665.77331 198847.047  0.41%   7.1    7s\n", "H 7868  1382                    199631.52331 198946.006  0.34%   8.4    7s\n", "H 8508  1476                    199601.52331 198999.233  0.30%   9.2    8s\n", "H 8524  1419                    199586.52331 198999.233  0.29%   9.2    8s\n", " 12715  2419 199416.704   26  202 199586.523 199167.316  0.21%  10.9   10s\n", "H22405  5636                    199571.52331 199291.636  0.14%   9.6   11s\n", "H28731  7213                    199546.58593 199329.676  0.11%   8.7   12s\n", "*28734  7078             133    199542.64773 199329.676  0.11%   8.7   12s\n", " 29810  7673 199537.719   99   31 199542.648 199335.769  0.10%   8.6   15s\n", " 63100 18789     cutoff  105      199542.648 199421.137  0.06%   6.5   20s\n", "H86544 23361                    199529.05248 199443.261  0.04%   5.9   22s\n", " 104521 29161     cutoff   92      199529.052 199452.250  0.04%   5.5   25s\n", " 142376 40740 199508.656   92   45 199529.052 199461.900  0.03%   4.9   30s\n", " 184848 49920 199486.787   90   44 199529.052 199469.082  0.03%   4.5   35s\n", " 214388 55321 199495.849   88   45 199529.052 199473.063  0.03%   4.4   40s\n", " 252335 60509 199509.225   95   36 199529.052 199477.666  0.03%   4.3   45s\n", " 293340 65501 199516.969   96   35 199529.052 199481.597  0.02%   4.2   50s\n", " 329337 68995 199506.734   95   37 199529.052 199485.138  0.02%   4.2   55s\n", " 367290 71395 199526.109   97   38 199529.052 199488.833  0.02%   4.1   60s\n", " 405807 72938 199527.073   84   51 199529.052 199492.270  0.02%   4.1   65s\n", " 442246 73986 199495.480   79   46 199529.052 199495.304  0.02%   4.1   70s\n", " 477530 74386 199527.384   98   38 199529.052 199497.966  0.02%   4.1   75s\n", " 515446 74503 199510.981  104   26 199529.052 199500.604  0.01%   4.1   80s\n", " 552177 73989     cutoff  113      199529.052 199502.914  0.01%   4.0   85s\n", " 585381 67077     cutoff   86      199529.052 199506.549  0.01%   4.1   90s\n", "\n", "Cutting planes:\n", "  Gomory: 56\n", "  Implied bound: 19\n", "  MIR: 336\n", "  Flow cover: 213\n", "  Flow path: 84\n", "  Inf proof: 23\n", "  Relax-and-lift: 2\n", "\n", "Explored 612260 nodes (2526594 simplex iterations) in 93.98 seconds (51.79 work units)\n", "Thread count was 8 (of 8 available processors)\n", "\n", "Solution count 10: 199529 199543 199547 ... 199711\n", "\n", "Optimal solution found (tolerance 1.00e-04)\n", "Best objective 1.995290524756e+05, best bound 1.995091076332e+05, gap 0.0100%\n", "Gurobi status= 2\n", "ステータス: Optimal\n", "総コスト: 199529.0524756493\n", "\n", "=== 最適化結果 ===\n", "最良個体の総コスト: 199529.05\n", "計算時間: 94.19秒\n", "結果をCSVファイルに保存: result//MIP_results_D40.csv\n", "\n", "=== 結果のプロット ===\n", "プロットを保存: result//MIP_results_D40.png\n", "時間制約違反: 0 期間\n", "\n", "集計結果をCSVファイルに保存: result//MIP_aggregate_results.csv\n", "\n", "=== 全体の集計結果 ===\n", "処理したファイル数: 3\n", "総目的関数値: 1070574.33\n", "総計算時間: 741.17秒\n", "平均目的関数値: 356858.11\n", "平均計算時間: 247.06秒\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1600x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1600x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1600x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import pulp\n", "import csv\n", "import random\n", "import matplotlib.pyplot as plt\n", "import japanize_matplotlib\n", "import copy\n", "\n", "# グローバル変数\n", "品番リスト = []\n", "出荷数リスト = []\n", "収容数リスト = []\n", "サイクルタイムリスト = []\n", "込め数リスト = []\n", "初期在庫量リスト = []\n", "\n", "# コストとペナルティの係数\n", "在庫コスト単価 = 180\n", "残業コスト単価 = 66.7\n", "段替えコスト単価 = 400\n", "出荷遅れコスト単価 = 500\n", "\n", "定時 = 8 * 60 * 2\n", "最大残業時間 = 2 * 60 * 2\n", "段替え時間 = 30\n", "\n", "def read_csv(file_path):\n", "    \"\"\"CSVファイルを読み込む関数\"\"\"\n", "    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "    \n", "    収容数辞書 = {}\n", "    with open('収容数.csv', 'r', encoding='shift-jis') as capacity_file:\n", "        capacity_reader = csv.reader(capacity_file)\n", "        capacity_header = next(capacity_reader)\n", "        for row in capacity_reader:\n", "            if len(row) >= 2 and row[1].strip():  # Skip if second column is empty\n", "                品番 = row[0]  # 品番列\n", "                収容数 = int(float(row[1]))  # 収容数列\n", "                収容数辞書[品番] = 収容数\n", "\n", "    with open(file_path, 'r', encoding='shift-jis') as file:\n", "        reader = csv.reader(file)\n", "        header = next(reader)\n", "        \n", "        品番リスト = []\n", "        出荷数リスト = []\n", "        収容数リスト = []\n", "        サイクルタイムリスト = []\n", "        込め数リスト = []\n", "        \n", "        # 期間数（日数）を定義\n", "        期間数 = 20\n", "        \n", "        rows = list(reader)\n", "        for row in rows:\n", "            if len(row) == 0:\n", "                continue\n", "            \n", "            # 個数を取得\n", "            total_quantity = int(row[header.index(\"個数\")])\n", "            \n", "            # 個数が200未満の場合はスキップ\n", "            if total_quantity < 200:\n", "                continue\n", "            \n", "            # 1日あたりの出荷数を計算（総期間で割る）\n", "            daily_quantity = total_quantity / 期間数\n", "            \n", "            品番リスト.append(row[header.index(\"素材品番\")])\n", "            出荷数リスト.append(daily_quantity)\n", "            込め数リスト.append(int(float(row[header.index(\"込数\")])))\n", "            \n", "            cycle_time_per_unit = float(row[header.index(\"サイクルタイム\")]) / 60\n", "            サイクルタイムリスト.append(cycle_time_per_unit)\n", "            \n", "            収容数リスト.append(収容数辞書.get(品番, 80))\n", "            \n", "    \n", "        # 出荷数に基づいて初期在庫量（処理前）をランダムに設定\n", "        初期在庫量リスト = []\n", "        for shipment in 出荷数リスト:\n", "            random_inventory = random.randint(int(shipment * 3), int(shipment * 5))\n", "            初期在庫量リスト.append(random_inventory)\n", "            \n", "    return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "\n", "def solve_mip(initial_inventory_list_arg):\n", "    \"\"\"PuLPを用いてMIPを解く関数\"\"\"\n", "    \n", "    # モデルの定義\n", "    model = pulp.LpProblem(\"ProductionScheduling\", pulp.LpMinimize)\n", "    \n", "    # インデックスの定義\n", "    品目 = range(品番数)\n", "    期間_index = range(期間)\n", "\n", "    # 決定変数\n", "    Production = pulp.LpVariable.dicts(\"Production\", (品目, 期間_index), lowBound=0, cat='Integer')\n", "    IsProduced = pulp.LpVariable.dicts(\"IsProduced\", (品目, 期間_index), cat='Binary')\n", "    Inventory = pulp.LpVariable.dicts(\"Inventory\", (品目, 期間_index), lowBound=0, cat='Continuous')\n", "    Shortage = pulp.LpVariable.dicts(\"Shortage\", (品目, 期間_index), lowBound=0, cat='Continuous')\n", "    WorkTime = pulp.LpVariable.dicts(\"WorkTime\", 期間_index, lowBound=0, cat='Continuous')\n", "    Overtime = pulp.LpVariable.dicts(\"Overtime\", 期間_index, lowBound=0, cat='Continuous')\n", "\n", "    # 目的関数\n", "    total_cost = pulp.lpSum(\n", "        在庫コスト単価 * Inventory[i][t]/収容数リスト[i] for i in 品目 for t in 期間_index\n", "    ) + pulp.lpSum(\n", "        残業コスト単価 * Overtime[t] for t in 期間_index\n", "    ) + pulp.lpSum(\n", "        段替えコスト単価 * IsProduced[i][t] for i in 品目 for t in 期間_index\n", "    ) + pulp.lpSum(\n", "        出荷遅れコスト単価 * Shortage[i][t] for i in 品目 for t in 期間_index\n", "    )\n", "    \n", "    model += total_cost, \"Total Cost\"\n", "\n", "    # 制約条件\n", "    bigM = 1000000\n", "\n", "    for i in 品目:\n", "        for t in 期間_index:\n", "            if t == 0:\n", "                # 初期在庫リストを使用\n", "                model += Inventory[i][t] - Shortage[i][t] == initial_inventory_list_arg[i] + Production[i][t] - 出荷数リスト[i]\n", "            else:\n", "                model += Inventory[i][t] - Shortage[i][t] == Inventory[i][t-1] - Shortage[i][t-1] + Production[i][t] - 出荷数リスト[i]\n", "            \n", "            model += Production[i][t] <= bigM * IsProduced[i][t]\n", "\n", "    for t in 期間_index:\n", "        model += WorkTime[t] == pulp.lpSum(\n", "            Production[i][t] * (サイクルタイムリスト[i] / 込め数リスト[i]) + 段替え時間 * IsProduced[i][t]\n", "            for i in 品目\n", "        )\n", "        \n", "        model += WorkTime[t] <= 定時 + Overtime[t]\n", "        model += WorkTime[t] <= 定時 + 最大残業時間\n", "        model += Overtime[t] >= WorkTime[t] - 定時\n", "        model += Overtime[t] >= 0\n", "\n", "    # Solverの設定\n", "    solver = pulp.GUROBI(msg=True, timelimit=500)\n", "    \n", "    # 最適化の実行\n", "    model.solve(solver)\n", "    \n", "    # ソルバーの詳細情報を取得\n", "    status = pulp.LpStatus[model.status]\n", "    print(\"ステータス:\", status)\n", "    \n", "    # 精度情報を格納する辞書\n", "    accuracy_info = {}\n", "    \n", "    # Gurobiソルバーの詳細情報を取得\n", "    if hasattr(solver, 'solverModel') and solver.solverModel is not None:\n", "        gurobi_model = solver.solverModel\n", "        \n", "        # 最適性ギャップの取得\n", "        if hasattr(gurobi_model, 'MIPGap'):\n", "            gap = gurobi_model.MIPGap * 100  # パーセンテージに変換\n", "            accuracy_info['gap'] = gap\n", "            print(f\"最適性ギャップ: {gap:.4f}%\")\n", "        \n", "        # 目的関数値と境界値の取得\n", "        if hasattr(gurobi_model, 'ObjVal'):\n", "            obj_val = gurobi_model.ObjVal\n", "            accuracy_info['obj_val'] = obj_val\n", "            print(f\"目的関数値: {obj_val:.2f}\")\n", "            \n", "        if hasattr(gurobi_model, 'ObjBound'):\n", "            obj_bound = gurobi_model.ObjBound\n", "            accuracy_info['obj_bound'] = obj_bound\n", "            print(f\"目的関数境界値: {obj_bound:.2f}\")\n", "            \n", "        # ソルバーステータスの詳細\n", "        if hasattr(gurobi_model, 'Status'):\n", "            gurobi_status = gurobi_model.Status\n", "            accuracy_info['gurobi_status'] = gurobi_status\n", "            print(f\"Gurobiステータス: {gurobi_status}\")\n", "            \n", "            # ステータスの意味を表示\n", "            status_meanings = {\n", "                1: \"LOADED (モデルが読み込まれた)\",\n", "                2: \"OPTIMAL (最適解が見つかった)\",\n", "                3: \"INFEASIBLE (実行不可能)\",\n", "                4: \"INF_OR_UNBD (実行不可能または非有界)\",\n", "                5: \"UNBOUNDED (非有界)\",\n", "                6: \"CUTOFF (カットオフ値により終了)\",\n", "                7: \"ITERATION_LIMIT (反復回数制限により終了)\",\n", "                8: \"NODE_LIMIT (ノード数制限により終了)\",\n", "                9: \"TIME_LIMIT (時間制限により終了)\",\n", "                10: \"SOLUTION_LIMIT (解の数制限により終了)\",\n", "                11: \"INTERRUPTED (ユーザーにより中断)\",\n", "                12: \"NUMERIC (数値的困難)\",\n", "                13: \"SUBOPTIMAL (準最適解)\",\n", "                14: \"INPROGRESS (進行中)\",\n", "                15: \"USER_OBJ_LIMIT (ユーザー目的関数制限により終了)\"\n", "            }\n", "            if gurobi_status in status_meanings:\n", "                accuracy_info['status_meaning'] = status_meanings[gurobi_status]\n", "                print(f\"ステータスの意味: {status_meanings[gurobi_status]}\")\n", "    \n", "    if status == 'Optimal':\n", "        print(\"総コスト:\", pulp.value(model.objective))\n", "\n", "        production_schedule = [[0] * 期間 for _ in range(品番数)]\n", "        for i in 品目:\n", "            for t in 期間_index:\n", "                production_schedule[i][t] = pulp.value(Production[i][t])\n", "\n", "        return production_schedule, pulp.value(model.objective), accuracy_info\n", "    elif status == 'Not Solved':\n", "        # 時間制限などで最適解が見つからなかった場合でも、実行可能解があれば取得\n", "        if pulp.value(model.objective) is not None:\n", "            print(\"時間制限により最適解は見つかりませんでしたが、実行可能解を取得しました\")\n", "            print(\"総コスト:\", pulp.value(model.objective))\n", "            \n", "            production_schedule = [[0] * 期間 for _ in range(品番数)]\n", "            for i in 品目:\n", "                for t in 期間_index:\n", "                    production_schedule[i][t] = pulp.value(Production[i][t])\n", "\n", "            return production_schedule, pulp.value(model.objective), accuracy_info\n", "    \n", "    return None, None, accuracy_info\n", "\n", "\n", "def simulate_production_schedule(initial_inventory, 期間=20):\n", "    \"\"\"生産スケジュールをシミュレートする関数（MIP用の簡易版）\"\"\"\n", "    品番数 = len(initial_inventory)\n", "    inventory = initial_inventory[:]\n", "    inventory_history = [[] for _ in range(品番数)]\n", "    \n", "    daily_regular_time = 8 * 60 * 2  # 1日の通常稼働時間（分）\n", "    max_daily_overtime = 2 * 60 * 2  # 1日の最大残業時間（分）\n", "    max_daily_work_time = daily_regular_time + max_daily_overtime\n", "    \n", "    for t in range(期間):\n", "        daily_production_time = 0\n", "        daily_setup_count = 0\n", "        \n", "        # 各品番の需要を処理\n", "        for i in range(品番数):\n", "            demand = 出荷数リスト[i]\n", "            inventory[i] -= demand\n", "            \n", "            # 在庫が不足する場合は生産\n", "            if inventory[i] < 0:\n", "                shortage = abs(inventory[i])\n", "                # 生産量を決定（不足分を補う）\n", "                production = shortage\n", "                \n", "                # 生産時間を計算\n", "                if production > 0:\n", "                    daily_setup_count += 1\n", "                    setup_time = 30  # 段替え時間\n", "                    production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "                    daily_production_time += production_time + setup_time\n", "                \n", "                inventory[i] += production\n", "            \n", "            # 在庫履歴に記録\n", "            inventory_history[i].append(max(0, inventory[i]))\n", "        \n", "        # 稼働時間制約チェック（制約違反の場合は一部生産を削減）\n", "        if daily_production_time > max_daily_work_time:\n", "            # 簡易的な調整：超過分を比例配分で削減\n", "            reduction_factor = max_daily_work_time / daily_production_time\n", "            for i in range(品番数):\n", "                if inventory[i] > 0:\n", "                    # 生産量を削減し、在庫を調整\n", "                    current_production = max(0, 出荷数リスト[i] - (initial_inventory[i] - inventory[i]))\n", "                    reduced_production = current_production * reduction_factor\n", "                    inventory[i] = initial_inventory[i] + reduced_production - 出荷数リスト[i] * (t + 1)\n", "                    inventory[i] = max(0, inventory[i])\n", "                    inventory_history[i][-1] = inventory[i]\n", "    \n", "    return inventory_history\n", "\n", "def adjust_initial_inventory(初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価, num_simulations=50, max_iterations=3):\n", "    \"\"\"初期在庫を更新する関数（optimize_initial_inventoryと一致させた版）\"\"\"\n", "    \n", "    品番数 = len(初期在庫量リスト)\n", "    s = 初期在庫量リスト[:]\n", "    \n", "    # h / (h+c) - optimize_initial_inventoryと同じ計算方法\n", "    prob_target = 在庫コスト単価 / (在庫コスト単価 + 出荷遅れコスト単価)\n", "    \n", "    print(f\"\\n=== 初期在庫水準の調整アルゴリズム開始 ===\")\n", "    \n", "    for iteration in range(max_iterations):\n", "        print(f\"--- 調整イテレーション {iteration + 1} ---\")\n", "        \n", "        # 各在庫点について在庫量の分布を求める\n", "        inventory_distributions = [[] for _ in range(品番数)]\n", "        for _ in range(num_simulations):\n", "            inventory_history = simulate_production_schedule(s)\n", "            for i in range(品番数):\n", "                inventory_distributions[i].extend(inventory_history[i])\n", "        \n", "        adjustments = [0] * 品番数\n", "        \n", "        # 各在庫点について在庫量の最適調整量r^*を求める\n", "        for i in range(品番数):\n", "            if not inventory_distributions[i]:\n", "                continue\n", "            \n", "            inventory_counts = pd.Series(inventory_distributions[i]).value_counts(normalize=True).sort_index()\n", "            cumulative_distribution = inventory_counts.cumsum()\n", "            \n", "            # sum_{x <= r-1} f(x) <= h / (h + c) - optimize_initial_inventoryと同じロジック\n", "            best_r = 0\n", "            for r in cumulative_distribution.index:\n", "                prob_at_r_minus_1 = cumulative_distribution.get(r - 1, 0)\n", "                if prob_at_r_minus_1 <= prob_target:\n", "                    best_r = r\n", "                else:\n", "                    break\n", "            \n", "            adjustments[i] = s[i] - best_r\n", "            \n", "        print(f\"  今回の調整量: {adjustments}\")\n", "        \n", "        # すべての在庫点での初期在庫量をs:=s-r^*によって更新する\n", "        new_s = [s[i] - adjustments[i] for i in range(品番数)]\n", "        \n", "        # 終了条件のチェック - optimize_initial_inventoryと同じ条件\n", "        if all(abs(adj) < 1 for adj in adjustments):\n", "            print(\"--- アルゴリズムが収束しました。---\")\n", "            return s\n", "            \n", "        s = new_s\n", "        print(f\"  更新後の初期在庫量: {s}\")\n", "        \n", "    print(\"--- 最大反復回数に到達しました。---\")\n", "    return s\n", "\n", "def plot_results(best_individual, initial_inventory, save_path=None):\n", "    \"\"\"結果をプロットする関数\"\"\"\n", "    global 品番数, 期間, 品番リスト, 出荷数リスト, サイクルタイムリスト, 込め数リスト\n", "    \n", "    print(\"\\n=== 結果のプロット ===\")\n", "    \n", "    total_inventory_per_period = []\n", "    total_production_time_per_period = []\n", "    total_setup_times_per_period = []\n", "    total_shipment_delay_per_period = []\n", "\n", "    inventory = initial_inventory[:]\n", "    max_daily_work_time = (8 + 2) * 60 * 2\n", "    daily_regular_time = 8 * 60 * 2\n", "    \n", "    for t in range(期間):\n", "        daily_inventory = 0\n", "        daily_production_time = 0\n", "        daily_setup_times = 0\n", "        daily_shipment_delay = 0\n", "        \n", "        for i in range(品番数):\n", "            production = best_individual[i][t]\n", "            \n", "            if production > 0:\n", "                daily_setup_times += 1\n", "                setup_time = 30\n", "            else:\n", "                setup_time = 0\n", "\n", "            production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "            daily_production_time += production_time + setup_time\n", "            \n", "            inventory[i] += production - 出荷数リスト[i]\n", "\n", "            if inventory[i] < 0:\n", "                daily_shipment_delay += abs(inventory[i])\n", "                inventory[i] = 0\n", "\n", "            daily_inventory += inventory[i]\n", "            \n", "        total_inventory_per_period.append(daily_inventory)\n", "        total_production_time_per_period.append(daily_production_time)\n", "        total_setup_times_per_period.append(daily_setup_times)\n", "        total_shipment_delay_per_period.append(daily_shipment_delay)\n", "\n", "    fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "    periods = list(range(1, 期間 + 1))\n", "\n", "    # 1. 各期間の総在庫量\n", "    axes[0, 0].bar(periods, total_inventory_per_period, color='skyblue', alpha=0.7)\n", "    axes[0, 0].set_title('各期間の総在庫量')\n", "    axes[0, 0].set_xlabel('期間')\n", "    axes[0, 0].set_ylabel('総在庫量 (個)')\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "    if total_inventory_per_period:\n", "        axes[0, 0].set_ylim(0, max(total_inventory_per_period) * 1.2)\n", "\n", "    # 2. 各期間の総生産時間＋制限ライン\n", "    axes[0, 1].bar(periods, total_production_time_per_period, color='lightgreen', alpha=0.7)\n", "    axes[0, 1].axhline(y=max_daily_work_time, color='red', linestyle='--', alpha=0.8, label=f'上限 ({max_daily_work_time}分)')\n", "    axes[0, 1].axhline(y=daily_regular_time, color='green', linestyle='--', alpha=0.8, label=f'定時 ({daily_regular_time}分)')\n", "    axes[0, 1].set_title('各期間の総生産時間')\n", "    axes[0, 1].set_xlabel('期間')\n", "    axes[0, 1].set_ylabel('総稼働時間 (分)')\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "    if total_production_time_per_period:\n", "        axes[0, 1].set_ylim(0, max(max_daily_work_time, max(total_production_time_per_period)) * 1.2)\n", "    axes[0, 1].legend()\n", "\n", "    # 3. 各期間の総段替え回数\n", "    axes[1, 0].bar(periods, total_setup_times_per_period, color='salmon', alpha=0.7)\n", "    axes[1, 0].set_title('各期間の総段替え回数')\n", "    axes[1, 0].set_xlabel('期間')\n", "    axes[1, 0].set_ylabel('総段替え回数（回）')\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "    if total_setup_times_per_period:\n", "        axes[1, 0].set_ylim(0, max(total_setup_times_per_period) * 1.2)\n", "\n", "    # 4. 各期間の総出荷遅れ量\n", "    axes[1, 1].bar(periods, total_shipment_delay_per_period, color='gold', alpha=0.7)\n", "    axes[1, 1].set_title('各期間の総出荷遅れ量')\n", "    axes[1, 1].set_xlabel('期間')\n", "    axes[1, 1].set_ylabel('総出荷遅れ量 (個)')\n", "    axes[1, 1].grid(True, alpha=0.3)\n", "    if total_shipment_delay_per_period:\n", "        axes[1, 1].set_ylim(0, max(total_shipment_delay_per_period) * 1.2)\n", "\n", "    plt.tight_layout()\n", "    \n", "    if save_path:\n", "        plt.savefig(save_path, dpi=300, bbox_inches='tight')\n", "        print(f\"プロットを保存: {save_path}\")\n", "    else:\n", "        plt.show()\n", "\n", "    time_violations = sum(1 for x in total_production_time_per_period if x > daily_regular_time + max_daily_work_time)\n", "    print(f\"時間制約違反: {time_violations} 期間\")\n", "    \n", "import os\n", "import time\n", "    \n", "def process_single_file(file_path):\n", "    \"\"\"単一のCSVファイルを処理する関数\"\"\"\n", "    global 品番数, 期間, 初期在庫量リスト\n", "    \n", "    print(f\"\\n=== Processing {file_path} ===\")\n", "    \n", "    # CSVファイルを読み込み\n", "    品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト = read_csv(file_path)\n", "    品番数 = len(品番リスト)\n", "    期間 = 20\n", "    在庫コスト単価 = 180\n", "    出荷遅れコスト単価 = 500\n", "    \n", "    # 初期在庫量を調整\n", "    adjusted_initial_inventory = adjust_initial_inventory(初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価, num_simulations=50, max_iterations=3)\n", "    初期在庫量リスト = adjusted_initial_inventory\n", "    \n", "    print(\"=== 混合整数計画法 スケジューリング ===\")\n", "    \n", "    # 計算時間を測定\n", "    start_time = time.time()\n", "    best_solution, best_cost, accuracy_info = solve_mip(初期在庫量リスト)\n", "    calculation_time = time.time() - start_time\n", "    \n", "    if best_solution:\n", "        print(f\"\\n=== 最適化結果 ===\")\n", "        print(f\"最良個体の総コスト: {best_cost:.2f}\")\n", "        print(f\"計算時間: {calculation_time:.2f}秒\")\n", "        \n", "        # 精度情報の表示\n", "        if accuracy_info:\n", "            print(f\"\\n=== 解の精度情報 ===\")\n", "            if 'gap' in accuracy_info:\n", "                print(f\"最適性ギャップ: {accuracy_info['gap']:.4f}%\")\n", "            if 'obj_val' in accuracy_info and 'obj_bound' in accuracy_info:\n", "                print(f\"目的関数値: {accuracy_info['obj_val']:.2f}\")\n", "                print(f\"目的関数境界値: {accuracy_info['obj_bound']:.2f}\")\n", "                gap_manual = abs(accuracy_info['obj_val'] - accuracy_info['obj_bound']) / abs(accuracy_info['obj_val']) * 100\n", "                print(f\"手動計算ギャップ: {gap_manual:.4f}%\")\n", "            if 'gurobi_status' in accuracy_info:\n", "                print(f\"Gurobiステータス: {accuracy_info['gurobi_status']}\")\n", "            if 'status_meaning' in accuracy_info:\n", "                print(f\"ステータスの意味: {accuracy_info['status_meaning']}\")\n", "        \n", "        # 結果をDataFrameに変換\n", "        result_df = pd.DataFrame(best_solution, \n", "                                index=[f\"品番_{i+1}\" for i in range(品番数)],\n", "                                columns=[f\"期間_{t+1}\" for t in range(期間)])\n", "        \n", "        # ファイル名から拡張子を除去\n", "        base_name = os.path.splitext(os.path.basename(file_path))[0]\n", "        \n", "        # 結果をCSVとして保存\n", "        result_csv_path = f\"result//MIP_results_{base_name}.csv\"\n", "        result_df.to_csv(result_csv_path, encoding='shift-jis')\n", "        print(f\"結果をCSVファイルに保存: {result_csv_path}\")\n", "        \n", "        # プロットを作成して保存\n", "        plot_path = f\"result//MIP_results_{base_name}.png\"\n", "        plot_results(best_solution, 初期在庫量リスト, save_path=plot_path)\n", "        \n", "        return best_cost, calculation_time, base_name\n", "    else:\n", "        print(\"\\n解が見つかりませんでした\")\n", "        return None, None, None\n", "\n", "def main():\n", "    \"\"\"メイン実行関数 - 全CSVファイルを処理\"\"\"\n", "    data_folder = \"data\"\n", "    \n", "    # dataフォルダ内のすべてのCSVファイルを取得\n", "    csv_files = [f for f in os.listdir(data_folder) if f.endswith('.csv')]\n", "    \n", "    if not csv_files:\n", "        print(\"dataフォルダにCSVファイルが見つかりません\")\n", "        return\n", "    \n", "    print(f\"見つかったCSVファイル: {csv_files}\")\n", "    \n", "    # 結果を格納するリスト\n", "    all_results = []\n", "    total_objective_value = 0\n", "    total_calculation_time = 0\n", "    \n", "    # 各CSVファイルを処理\n", "    for csv_file in csv_files:\n", "        file_path = os.path.join(data_folder, csv_file)\n", "        objective_value, calc_time, file_name = process_single_file(file_path)\n", "        \n", "        if objective_value is not None:\n", "            all_results.append({\n", "                'ファイル名': file_name,\n", "                '目的関数値': objective_value,\n", "                '計算時間': calc_time\n", "            })\n", "            total_objective_value += objective_value\n", "            total_calculation_time += calc_time\n", "    \n", "    # 集計結果をDataFrameに変換\n", "    summary_df = pd.DataFrame(all_results)\n", "    \n", "    # 合計行を追加\n", "    summary_row = pd.DataFrame({\n", "        'ファイル名': ['合計'],\n", "        '目的関数値': [total_objective_value],\n", "        '計算時間': [total_calculation_time]\n", "    })\n", "    summary_df = pd.concat([summary_df, summary_row], ignore_index=True)\n", "    \n", "    # 集計結果をCSVとして保存\n", "    summary_csv_path = \"result//MIP_aggregate_results.csv\"\n", "    summary_df.to_csv(summary_csv_path, encoding='shift-jis', index=False)\n", "    print(f\"\\n集計結果をCSVファイルに保存: {summary_csv_path}\")\n", "    \n", "    # 集計結果のプロットを作成\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # 目的関数値のプロット（合計を除く）\n", "    individual_results = summary_df[summary_df['ファイル名'] != '合計']\n", "    ax1.bar(individual_results['ファイル名'], individual_results['目的関数値'], \n", "            color='skyblue', alpha=0.7)\n", "    ax1.set_title('各データセットの目的関数値')\n", "    ax1.set_xlabel('データセット')\n", "    ax1.set_ylabel('目的関数値')\n", "    ax1.tick_params(axis='x', rotation=45)\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # 計算時間のプロット（合計を除く）\n", "    ax2.bar(individual_results['ファイル名'], individual_results['計算時間'], \n", "            color='lightgreen', alpha=0.7)\n", "    ax2.set_title('各データセットの計算時間')\n", "    ax2.set_xlabel('データセット')\n", "    ax2.set_ylabel('計算時間 (秒)')\n", "    ax2.tick_params(axis='x', rotation=45)\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    \n", "    # 集計プロットを保存\n", "    #aggregate_plot_path = \"aggregate_results.png\"\n", "    #plt.savefig(aggregate_plot_path, dpi=300, bbox_inches='tight')\n", "    #plt.show()\n", "    #print(f\"集計プロットを画像ファイルに保存: {aggregate_plot_path}\")\n", "    \n", "    # 結果の要約を表示\n", "    print(f\"\\n=== 全体の集計結果 ===\")\n", "    print(f\"処理したファイル数: {len(all_results)}\")\n", "    print(f\"総目的関数値: {total_objective_value:.2f}\")\n", "    print(f\"総計算時間: {total_calculation_time:.2f}秒\")\n", "    print(f\"平均目的関数値: {total_objective_value/len(all_results):.2f}\")\n", "    print(f\"平均計算時間: {total_calculation_time/len(all_results):.2f}秒\")\n", "    \n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "markdown", "id": "2ee490f6", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}